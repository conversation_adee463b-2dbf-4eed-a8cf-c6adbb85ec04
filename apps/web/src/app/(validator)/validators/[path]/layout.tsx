import { notFound } from 'next/navigation';
import { ValidatorHeaderDynamic } from '@/components/views/validators/validator-header.dynamic';
import { encodeSS58Address } from '@/lib/utils/encoding';

export default function TabsLayout({
  tabs,
  tables,
  params: { path },
}: {
  tabs: React.ReactNode;
  tables: React.ReactNode;
  params: { path: string };
}) {
  let hotkey: string;
  try {
    hotkey = encodeSS58Address(path);
  } catch {
    notFound();
  }

  return (
    <div>
      <ValidatorHeaderDynamic path={hotkey} />
      {/* {tabs}
      {tables} */}
    </div>
  );
}
