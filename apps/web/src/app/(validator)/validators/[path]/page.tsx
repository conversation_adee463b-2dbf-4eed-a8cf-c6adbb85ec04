import { ValidatorTableGroup } from '@/components/views/validators/validator-table-group';

// export async function generateMetadata({
//   params: { path },
// }: {
//   params: { path: string };
// }): Promise<Metadata | null | undefined> {
//   const validatorName = await fetchIdentityId(path);

//   const truncatedValidatorName =
//     validatorName.length > 8
//       ? `${validatorName.slice(0, 8)}...`
//       : validatorName;

//   return constructMetadata({
//     title: `${truncatedValidatorName} · Validator · taostats`,
//   });
// }

export default function ValidatorDetailsPage({
  params: { path },
}: {
  params: { path: string };
}) {
  return (
    <div className='min-h-[30rem] px-2 py-4 md:px-12'>
      Hello
      {/* <ValidatorTableGroup address={path} /> */}
    </div>
  );
}
