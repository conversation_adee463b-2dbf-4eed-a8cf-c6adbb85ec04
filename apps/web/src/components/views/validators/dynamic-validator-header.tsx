'use client';

import { useEffect, useMemo } from 'react';
import { utc } from 'moment';
import { format } from 'numerable';
import { BiListPlus, BiShield } from 'react-icons/bi';
import { CgArrowRight } from 'react-icons/cg';
import { MdCallReceived, MdOutlineNorthEast } from 'react-icons/md';
import { AddressFormatter, Button, Link, Text } from '@repo/ui/components';
import { appRoutes, cn, taoDivider, useWindowSize } from '@repo/ui/lib';
import { ValidatorSocialSection } from './validator-social-section';
import { Bittensor } from '@/components/icons/bittensor';
import { DtaoPercentage } from '@/components/views/dtao/dtao-percentage';
import { PriceFormatter } from '@/components/views/dtao/single-subnet/price-formatter';
import { SubnetCard } from '@/components/views/dtao/subnet-card';
import { useSingleValidatorHeader } from '@/lib/hooks';
import { useValidatorMetadataAtom } from '@/lib/hooks/use-validator-metadata';
import { tooltipDescription } from '@/lib/tooltip-description';
import { getIconUrl, truncateString, walletFormat } from '@/lib/utils';

export default function DynamicHeaderValidator({ path }: { path: string }) {
  const { windowSize } = useWindowSize();
  const { validatorMetadata } = useValidatorMetadataAtom();
  const { setDominance } = useValidatorDominance();
  const { data } = useSingleValidatorHeader({ hotkey: path });
  const validatorCore = useMemo(() => data?.data ?? [], [data]);

  const currentValidator = useMemo(
    () =>
      validatorMetadata.find(
        (item) => item.validator_hotkey?.ss58 === validatorCore.hotkey.ss58
      ),
    [validatorMetadata, validatorCore]
  );

  useEffect(() => {
    if (validatorCore.dominance !== undefined) {
      setDominance(Number(validatorCore.dominance));
    }
  }, [setDominance, validatorCore.dominance]);

  return (
    <div className='flex w-screen flex-col gap-16 overflow-hidden px-4 py-6 pb-8 md:px-16 md:py-10'>
      <div className='flex flex-col flex-wrap items-start justify-between gap-6 sm:flex-row sm:items-center'>
        <div className='flex w-full flex-col items-start gap-3 sm:w-max sm:flex-row sm:items-center'>
          <div className='flex flex-row items-center gap-3'>
            <div className='relative h-8 w-8 sm:h-12 sm:w-12'>
              {/* <ValidatorImage
                fill
                url={currentValidator?.image ?? ''}
                src={
                  currentValidator?.image.length
                    ? currentValidator.image ?? ''
                    : getIconUrl(path)
                }
                className='h-8 w-8 rounded-full sm:h-12 sm:w-12'
                quality={100}
                alt={`${currentValidator?.name ?? truncateString(path)} icon`}
              /> */}
            </div>
            <p className='sm:leading-15 flex flex-row items-center gap-3 whitespace-nowrap text-2xl font-medium leading-7 sm:text-5xl'>
              {currentValidator?.name ?? walletFormat(path)}
              {currentValidator?.name ? (
                <BiShield size={24} className='text-[#00DBBCCC]' />
              ) : null}
            </p>
          </div>
          <div className='h-7.5 rounded-full border border-[#323232] bg-[#1D1D1D] px-3 py-1'>
            <Text
              level='sm'
              className='whitespace-nowrap font-medium opacity-60'
            >
              Rank #{validatorCore.rank}
            </Text>
          </div>
        </div>
        <div className='flex w-full flex-col items-center gap-6 sm:w-max sm:flex-row'>
          <Button
            variant='ghost'
            className='flex h-10 flex-row items-center gap-2 rounded-lg border border-[#323232] bg-[#1D1D1D] px-4 py-2 max-sm:!w-full'
            asChild
          >
            {currentValidator?.url ? (
              <Link
                href={
                  currentValidator.url.startsWith('http')
                    ? currentValidator.url
                    : `https://${currentValidator.url}`
                }
              >
                <Text level='base' className='font-medium'>
                  Discover {currentValidator.name}
                </Text>
                <CgArrowRight size={24} />
              </Link>
            ) : null}
          </Button>
          <Button
            variant='ghost'
            className='flex h-10 flex-row items-center gap-2 rounded-lg border border-[#323232] bg-[#1D1D1D] px-4 py-2 max-sm:!w-full'
            asChild
          >
            <Link href={appRoutes.validator.stakeTao(path)} target='_blank'>
              <Text level='base' className='font-medium'>
                Delegate Stake
              </Text>
              <BiListPlus size={24} />
            </Link>
          </Button>
        </div>
      </div>
      <div className='3xl:grid-cols-11 grid grid-cols-10 gap-6'>
        <div className='3xl:col-span-4 order-1 col-span-10 rounded-xl bg-[#272727] p-5 lg:col-span-5'>
          <div className='flex flex-col gap-4'>
            <Text level='xs' className='opacity-60'>
              Total Stake Weight
            </Text>
            <div className='flex flex-col'>
              <PriceFormatter
                data={Number(validatorCore.global_weighted_stake) / taoDivider}
                prefix={
                  <Bittensor className='-ml-2.5 -mr-2 h-10 w-10 text-white' />
                }
                suffix={
                  <div className='flex flex-row items-end gap-3 max-sm:ml-2'>
                    <p className='ml-2 hidden items-end !text-4xl !leading-[44px] opacity-60 sm:block'>
                      TAO
                    </p>
                    <span
                      className={cn(
                        'leading-3.75 mb-2 flex h-max w-fit flex-row items-center gap-0.5 truncate rounded-full py-1 pl-1 pr-2 text-xs',
                        Number(
                          validatorCore.global_weighted_stake_24_hr_change
                        ) /
                          Number(validatorCore.global_weighted_stake) >=
                          0 && 'bg-[#00DBBC1A] text-[#00DBBC]',
                        Number(
                          validatorCore.global_weighted_stake_24_hr_change
                        ) /
                          Number(validatorCore.global_weighted_stake) <
                          0 && 'bg-[#EB53471A] text-[#EB5347]'
                      )}
                    >
                      {Number(
                        validatorCore.global_weighted_stake_24_hr_change
                      ) /
                        Number(validatorCore.global_weighted_stake) >=
                      0 ? (
                        <MdOutlineNorthEast size={24} className='p-1' />
                      ) : (
                        <MdCallReceived size={24} className='p-1' />
                      )}
                      {format(
                        Number(
                          validatorCore.global_weighted_stake_24_hr_change
                        ) / Number(validatorCore.global_weighted_stake),
                        '0.00'
                      )}
                      %
                    </span>
                  </div>
                }
                mainClassName='!text-white !text-5xl !leading-15 font-medium -mb-0.75'
                contentClassName='!text-white !text-4xl !leading-[44px] items-end'
              />
            </div>
            <DtaoPercentage
              root={validatorCore.weighted_root_stake ?? ''}
              stake={validatorCore.global_alpha_stake_as_tao ?? ''}
              actualStake={validatorCore.root_stake ?? ''}
              rightContent='Alpha'
              leftContent='Root'
              className='!w-full gap-2'
              barClassName='!h-3'
              bottom
            />
          </div>
        </div>
        <div className='3xl:order-2 3xl:col-span-4 order-2 col-span-10 grid w-full grid-cols-2 gap-6 lg:order-3'>
          <SubnetCard
            title='Nominator Return'
            tooltip
            tooltipDescription={tooltipDescription.validator.nominatorReturn}
            data={
              Number(validatorCore.nominator_return_per_day ?? 0) / taoDivider
            }
            prefix={<Bittensor className='-mb-0.75 -ml-3 -mr-1 h-9 w-9' />}
            className='col-span-2 justify-between sm:col-span-1'
            mainClassName='font-medium text-[40px] leading-[48px] -mb-1'
            contentClassName='font-normal text-2xl leading-7'
          />
          <SubnetCard
            title='Validator Return'
            tooltip
            tooltipDescription={tooltipDescription.validator.validatorReturn}
            data={Number(validatorCore.validator_return_per_day) / taoDivider}
            prefix={<Bittensor className='-mb-0.75 -ml-3 -mr-1 h-9 w-9' />}
            suffix={
              <Text
                level='base'
                className='ml-2 whitespace-nowrap leading-5 opacity-60'
              >
                from {format(Number(validatorCore.take) * 100, '0')}% take
              </Text>
            }
            className='col-span-2 justify-between sm:col-span-1'
            mainClassName='font-medium text-[40px] leading-[48px] -mb-1'
            contentClassName='font-normal text-2xl leading-7'
          />
          <SubnetCard
            title='Total Nominators'
            tooltip
            tooltipDescription={tooltipDescription.validator.totalNominators}
            data={validatorCore.global_nominators ?? ''}
            className='col-span-2 justify-between sm:col-span-1'
            mainClassName='font-medium text-[40px] leading-[48px] -mb-1'
            type='Number'
            suffix={
              <span
                className={cn(
                  'leading-3.75 mb-1.5 ml-3 flex h-max w-fit flex-row items-center gap-0.5 truncate rounded-full py-1 pl-1 pr-2 text-xs',
                  Number(validatorCore.global_nominators_24_hr_change) >= 0 &&
                    'bg-[#00DBBC1A] text-[#00DBBC]',
                  Number(validatorCore.global_nominators_24_hr_change) < 0 &&
                    'bg-[#EB53471A] text-[#EB5347]'
                )}
              >
                {Number(validatorCore.global_nominators_24_hr_change) >= 0 ? (
                  <MdOutlineNorthEast size={24} className='p-1' />
                ) : (
                  <MdCallReceived size={24} className='p-1' />
                )}
                {Number(validatorCore.global_nominators_24_hr_change) ?? 0}
              </span>
            }
          />
          <SubnetCard
            title='Dominance'
            tooltip
            tooltipDescription={tooltipDescription.validator.dominance}
            data={validatorCore.dominance ?? ''}
            className='col-span-2 justify-between sm:col-span-1'
            mainClassName='font-medium text-[40px] leading-[48px] -mb-1'
            contentClassName='font-normal text-2xl leading-7'
            suffix={
              <div className='flex flex-row items-end gap-3'>
                <p className='text-2xl font-normal leading-7'>%</p>
                <span
                  className={cn(
                    'leading-3.75 flex h-max w-fit flex-row items-center gap-0.5 truncate rounded-full py-1 pl-1 pr-2 text-xs',
                    Number(validatorCore.dominance_24_hr_change) >= 0 &&
                      'bg-[#00DBBC1A] text-[#00DBBC]',
                    Number(validatorCore.dominance_24_hr_change) < 0 &&
                      'bg-[#EB53471A] text-[#EB5347]'
                  )}
                >
                  {Number(validatorCore.dominance_24_hr_change) >= 0 ? (
                    <MdOutlineNorthEast size={24} className='p-1' />
                  ) : (
                    <MdCallReceived size={24} className='p-1' />
                  )}
                  {format(
                    Number(validatorCore.dominance_24_hr_change) ?? 0,
                    '0.00'
                  )}
                  %
                </span>
              </div>
            }
          />
        </div>
        <div className='3xl:order-3 3xl:col-span-3 3xl:pl-6 order-3 col-span-10 flex flex-col gap-6 lg:order-2 lg:col-span-5'>
          <div className='flex flex-col gap-4'>
            <Text level='base' className='font-normal opacity-60'>
              {currentValidator?.description ?? ''}
            </Text>
            <ValidatorSocialSection
              github={currentValidator?.github_repo ?? ''}
              validatorUrl={currentValidator?.url ?? ''}
              discord={currentValidator?.discord ?? ''}
            />
          </div>
          <div className='border border-b opacity-20' />
          <div className='flex flex-col gap-4'>
            <div className='fle flex-col gap-2'>
              <Text level='xs' className='opacity-60'>
                HotKey
              </Text>
              <div className='w-fit'>
                <AddressFormatter
                  uid={path}
                  className='text-sm'
                  noChange
                  fullWidth={
                    ((windowSize.width ?? 0) > 2000 ||
                      (windowSize.width ?? 0) < 1700) &&
                    (windowSize.width ?? 0) > 520
                  }
                  noLink
                  noIcon
                  textClassName='w-full'
                />
              </div>
            </div>
            <div className='fle flex-col gap-2'>
              <Text level='xs' className='opacity-60'>
                Registered To
              </Text>
              {validatorCore.coldkey &&
              validatorCore.coldkey.ss58.length > 0 ? (
                <div className='w-fit'>
                  <AddressFormatter
                    uid={validatorCore.coldkey.ss58}
                    className='text-sm'
                    noIcon
                    fullWidth={
                      ((windowSize.width ?? 0) > 2000 ||
                        (windowSize.width ?? 0) < 1700) &&
                      (windowSize.width ?? 0) > 520
                    }
                    account
                    textClassName='w-full'
                  />
                </div>
              ) : null}
            </div>
            <div className='fle flex-col gap-2'>
              <Text level='xs' className='opacity-60'>
                Registered On
              </Text>
              <Text level='sm' className='truncate'>
                {utc(validatorCore.created_on_date).format('MMM DD, YYYY')}
              </Text>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
