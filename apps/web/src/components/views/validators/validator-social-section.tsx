import Image from 'next/image';
import { Link } from '@repo/ui/components';
import { imagePaths } from '@/lib/image-paths';

export function ValidatorSocialSection({
  github,
  discord,
  validatorUrl,
}: {
  github: string;
  validatorUrl: string;
  discord: string;
}) {
  return (
    <div className='flex flex-row items-center gap-6'>
      {validatorUrl ? (
        <Link
          href={
            validatorUrl.startsWith('https://')
              ? validatorUrl
              : `https://${validatorUrl}`
          }
          target='_blank'
          className='text-3xl hover:opacity-70'
        >
          <Image
            src={imagePaths.logo.icon4}
            height={32}
            width={32}
            alt=''
            className='h-8 w-8 opacity-60'
          />
        </Link>
      ) : null}
      {github ? (
        <Link
          href={github}
          target='_blank'
          className='text-3xl hover:opacity-70'
        >
          <Image
            src={imagePaths.logo.github}
            height={32}
            width={32}
            alt=''
            className='h-8 w-8 opacity-60'
          />
        </Link>
      ) : null}
      {discord ? (
        <Link
          href={`https://discord.gg/${discord}`}
          target='_blank'
          className='text-3xl hover:opacity-70'
        >
          <Image
            src={imagePaths.logo.discord}
            height={32}
            width={32}
            alt=''
            className='h-8 w-8 opacity-60'
          />
        </Link>
      ) : null}
    </div>
  );
}
